<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle}">添加视频 - 视频播放器</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/admin-style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container my-4">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <!-- 页面标题 -->
                <div class="d-flex align-items-center mb-4">
                    <a href="/admin" class="btn btn-outline-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-plus-circle me-2 text-primary"></i>添加视频
                    </h1>
                </div>

                <!-- 添加视频表单 -->
                <div class="card shadow-sm">
                    <div class="card-body">
                        <form id="addVideoForm" enctype="multipart/form-data">
                            <!-- 视频文件上传 -->
                            <div class="mb-4">
                                <label for="videoFile" class="form-label">
                                    <i class="fas fa-video me-1"></i>视频文件 <span class="text-danger">*</span>
                                </label>
                                <input type="file" class="form-control" id="videoFile" name="videoFile" 
                                       accept="video/*" required>
                                <div class="form-text">支持 MP4、AVI、MOV 等格式，最大 100MB</div>
                                <div class="invalid-feedback"></div>
                            </div>

                            <!-- 缩略图上传 -->
                            <div class="mb-4">
                                <label for="thumbnailFile" class="form-label">
                                    <i class="fas fa-image me-1"></i>缩略图
                                </label>
                                <input type="file" class="form-control" id="thumbnailFile" name="thumbnailFile" 
                                       accept="image/*">
                                <div class="form-text">可选，支持 JPG、PNG 等格式</div>
                                <div class="invalid-feedback"></div>
                            </div>

                            <!-- 视频标题 -->
                            <div class="mb-3">
                                <label for="title" class="form-label">
                                    <i class="fas fa-heading me-1"></i>视频标题 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       placeholder="请输入视频标题" maxlength="200" required>
                                <div class="invalid-feedback"></div>
                            </div>

                            <!-- 视频描述 -->
                            <div class="mb-3">
                                <label for="description" class="form-label">
                                    <i class="fas fa-align-left me-1"></i>视频描述
                                </label>
                                <textarea class="form-control" id="description" name="description" 
                                          rows="3" placeholder="请输入视频描述" maxlength="500"></textarea>
                                <div class="invalid-feedback"></div>
                            </div>

                            <!-- 视频参数 -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="duration" class="form-label">
                                        <i class="fas fa-clock me-1"></i>时长（秒）
                                    </label>
                                    <input type="number" class="form-control" id="duration" name="duration" 
                                           placeholder="自动检测" min="1">
                                </div>
                                <div class="col-md-4">
                                    <label for="videoFormat" class="form-label">
                                        <i class="fas fa-file-video me-1"></i>格式
                                    </label>
                                    <select class="form-select" id="videoFormat" name="videoFormat">
                                        <option value="">自动检测</option>
                                        <option value="mp4">MP4</option>
                                        <option value="avi">AVI</option>
                                        <option value="mov">MOV</option>
                                        <option value="wmv">WMV</option>
                                        <option value="flv">FLV</option>
                                        <option value="webm">WebM</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="resolution" class="form-label">
                                        <i class="fas fa-expand me-1"></i>分辨率
                                    </label>
                                    <select class="form-select" id="resolution" name="resolution">
                                        <option value="">请选择</option>
                                        <option value="4K">4K (3840×2160)</option>
                                        <option value="1080p">1080p (1920×1080)</option>
                                        <option value="720p">720p (1280×720)</option>
                                        <option value="480p">480p (854×480)</option>
                                        <option value="360p">360p (640×360)</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 上传进度 -->
                            <div id="uploadProgress" class="mb-3" style="display: none;">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-cloud-upload-alt me-2 text-primary"></i>
                                    <span>正在上传...</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="/admin" class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>保存视频
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 全局提示容器 -->
    <div id="alertContainer" class="position-fixed top-0 start-50 translate-middle-x" style="z-index: 9999; margin-top: 20px;"></div>

    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div>正在处理，请稍候...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义JS -->
    <script src="/js/admin.js"></script>
    
    <script>
        // 初始化添加视频页面
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('addVideoForm');
            if (form) {
                form.addEventListener('submit', handleVideoUpload);
            }
        });

        // 处理视频上传
        async function handleVideoUpload(event) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            const progressContainer = document.getElementById('uploadProgress');
            const progressBar = progressContainer.querySelector('.progress-bar');
            
            try {
                // 验证表单
                if (!validateForm(form)) {
                    return;
                }
                
                // 显示上传进度
                progressContainer.style.display = 'block';
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>上传中...';
                
                // 模拟进度更新
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 15;
                    if (progress > 90) progress = 90;
                    progressBar.style.width = progress + '%';
                }, 500);
                
                // 上传文件
                const response = await fetch('/api/upload/video-with-thumbnail', {
                    method: 'POST',
                    body: formData
                });
                
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('视频上传成功！🎉', 'success');
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 1500);
                } else {
                    throw new Error(result.message || '上传失败');
                }
                
            } catch (error) {
                console.error('上传失败:', error);
                showAlert('上传失败: ' + error.message, 'danger');
                
                // 重置状态
                progressContainer.style.display = 'none';
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>保存视频';
            }
        }
        
        // 表单验证
        function validateForm(form) {
            const videoFile = form.querySelector('#videoFile');
            const title = form.querySelector('#title');
            
            if (!videoFile.files.length) {
                showAlert('请选择视频文件', 'warning');
                return false;
            }
            
            if (!title.value.trim()) {
                showAlert('请输入视频标题', 'warning');
                return false;
            }
            
            return true;
        }
        
        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert-' + Date.now();
            
            const alertHtml = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            alertContainer.insertAdjacentHTML('beforeend', alertHtml);
            
            // 自动移除提示
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    alertElement.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
