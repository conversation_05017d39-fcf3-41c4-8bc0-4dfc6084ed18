/**
 * 首页专用样式
 * Index Page Styles
 */

/* 导航栏高度调整 */
.navbar.navbar-dark.bg-primary.sticky-top {
    min-height: calc(48px); /* Bootstrap默认navbar高度56px + 增加6px */
    padding-top: calc(0.5rem + 3px);
    padding-bottom: calc(0.5rem + 3px);
}

/* 禁用移动端双击放大功能 */
* {
    touch-action: manipulation;
}

/* 允许文本选择 */
input, textarea, [contenteditable], p, h1, h2, h3, h4, h5, h6, span, div.content {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}
/* 导航栏样式 */
.navbar {
    /* min-height: calc(56px - 5px + 2.5px) !important; Bootstrap默认56px，减少5px，再增加2.5px = 53.5px */
    padding-top: 0.35rem !important;
    padding-bottom: 0.35rem !important;
}

/* 简化的容器样式 */
.container.my-4, main {
    opacity: 1;
    position: static;
    margin: 0!important;
}



.navbar .search-input {
    width: 250px;
}

.search-input-mobile {
    width: 150px;
}

/* 移动端导航栏优化 */
.navbar-nav .nav-link {
    font-size: 0.9rem;
    white-space: nowrap;
}


.back-to-top:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

/* 欢迎横幅 */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px !important;
    position: relative;
    overflow: visible;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin: 0 auto;
}

/* 标题和Logo容器位置调整 */
.title-with-logo {
    margin-top: 0px;
    margin-bottom: 0px;
    padding-top: 20px;
    padding-bottom: 20px;
    position: relative;
    z-index: 10;
    text-align: center;
    transform: translateY(-10px);
}

/* 标题文字位置调整 */
.title-with-logo .display-4 {
    margin-top: 1px;
    margin-bottom: 1px;
    font-weight: bold;
    transform: translateY(-4px);
}

/* h1标题专门调整 */
.title-with-logo h1 {
    margin-bottom: 1px !important;
    transform: translateY(-6px);
}

/* Logo图片位置调整 */
.title-with-logo img {
    margin-top: 0px;
    margin-bottom: 1px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    max-width: 100%;
    height: auto;
}

/* 移动端专用调整 */
@media (max-width: 768px) {
    .hero-section.mb-5 {
        transform: translateY(-10px) !important;
    }
    .title-with-logo {
        margin-top: 0px;
        padding-top: 15px;
        margin-bottom: 0px;
        transform: translateY(-10px);
    }

    .title-with-logo .display-4 {
        margin-top: 1px;
        margin-bottom: 1px;
        font-size: 2rem;
        transform: translateY(-4px);
    }

    .title-with-logo h1 {
        margin-bottom: 1px !important;
        transform: translateY(-6px);
    }

    .title-with-logo img {
        margin-top: 0px;
        margin-bottom: 1px;
        max-width: 150px;
        height: auto;
    }
}

/* 小屏幕移动端进一步调整 */
@media (max-width: 480px) {
    .hero-section.mb-5 {
        transform: translateY(-10px) !important;
    }
    .title-with-logo {
        margin-top: 0px;
        padding-top: 10px;
        margin-bottom: 0px;
        transform: translateY(-10px);
    }

    .title-with-logo .display-4 {
        margin-top: 1px;
        margin-bottom: 1px;
        font-size: 1.8rem;
        transform: translateY(-4px);
    }

    .title-with-logo h1 {
        margin-bottom: 1px !important;
        transform: translateY(-6px);
    }

    .title-with-logo img {
        margin-top: 0px;
        margin-bottom: 1px;
        max-width: 120px;
        height: auto;
    }
}
/* 联系信息显示容器 - 支持动态参数 */
.lead-contact-display {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', sans-serif;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 0.5rem 2rem;
    border: 2px solid rgba(255, 255, 255, 0.8);
    margin: 0px auto 0px auto;
    width: 100%;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    text-align: center;
    height: var(--contact-height, 168px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    /* 防止边距折叠 */
    overflow: hidden;
    transform: translateY(-15px);
}

.lead-contact-display p {
    margin: 0px 0px 8px 0px;
    font-size: 0.9rem;
    font-weight: 600;
    color: #2d3748;
    line-height: 1.5;
    text-align: center;
    width: 100%;
    flex-shrink: 0;
}

.lead-contact-display p:last-child {
    margin-bottom: 0;
}

/* 加载和错误状态样式 */
.loading-text, .error-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    padding: 1rem;
    font-size: 0.9rem;
    color: #666;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(5px);
    white-space: nowrap;
}

.mb-5 {
    margin-bottom: 0px !important;
    transform: translateY(-10px);
}

/* 专门针对hero-section的mb-5 */
.hero-section.mb-5 {
    transform: translateY(-10px) !important;
}
.mb-4{
    margin-bottom: 8px !important;
}

/* 按钮内边距调整 */
.btn.btn-outline-primary {
    padding: 3px 6px !important;
}

/* 视频缩略图链接 */
.video-thumbnail {
    display: block;
    text-decoration: none;
}

.video-thumbnail:hover {
    text-decoration: none;
}

/* 缩略图优化样式 */
.thumbnail-optimized {
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    opacity: 0.7;
    background-color: #f8f9fa;
    background-image:
        linear-gradient(45deg, #e9ecef 25%, transparent 25%),
        linear-gradient(-45deg, #e9ecef 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #e9ecef 75%),
        linear-gradient(-45deg, transparent 75%, #e9ecef 75%);
    background-size: 16px 16px;
    background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
}

.thumbnail-optimized.loaded {
    opacity: 1;
    background-image: none;
}

/* 缩略图悬停效果 */
.video-thumbnail:hover .thumbnail-optimized {
    transform: scale(1.02);
}

/* 缩略图加载状态 */
.thumbnail-optimized:not(.loaded) {
    position: relative;
}

.thumbnail-optimized:not(.loaded)::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 缩略图容器优化 */
.video-card .card-img-top {
    will-change: transform, opacity;
    backface-visibility: hidden;
}

/* 错误状态样式 */
.thumbnail-optimized.error {
    opacity: 0.8;
    filter: grayscale(20%);
}

/* 视频内容区域 */
.video-content {
    margin-bottom: 2rem;
}

/* 空状态样式 */
.empty-state {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.error-text {
    color: #e53e3e;
    background: rgba(229, 62, 62, 0.1);
}

/* 加载动画 */
.loading-text::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 8px;
    border: 2px solid #666;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}



/* 动画定义 - 已禁用 */
/*
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
*/

/* 响应式优化 */
@media (max-width: 768px) {



}

@media (max-width: 576px) {





}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
}

/* 调整欢迎横幅中的row位置 */
.hero-section .row {
    margin-top: 0rem !important;
    position: relative;
    z-index: 2;
    width: 100%;
    justify-content: center;
}

/* 调整col-12 text-center位置向上10px */
.hero-section .col-12.text-center {
    transform: translateY(-10px);
}

/* 移动端row位置调整 */
@media (max-width: 768px) {
    .hero-section .row {
        margin-top: 0px !important;
    }
}

/* 视频卡片 */
.video-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.video-card .card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.video-card:hover .card-img-top {
    transform: scale(1.05);
}

.video-card .card-body {
    padding: 1.25rem;
}

.video-card .card-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-card .card-text {
    color: #6c757d;
    font-size: 0.9rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 视频元数据 */
.video-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.video-duration {
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    position: absolute;
    bottom: 8px;
    right: 8px;
}

/* 加载更多按钮 */
.load-more-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 1rem;
}

/* 统计信息 */
.stats-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
}

.stat-item {
    text-align: center;
    padding: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    display: block;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-input-mobile {
        width: 120px;
    }



    .navbar {
        /* 移动端缩小后再增加2.5px = 45.5px */
        /* min-height: calc(48px - 5px + 2.5px) !important;  */
        padding-top: 0.25rem !important;
        padding-bottom: 0.25rem !important;
    }

    .navbar-nav .nav-link {
        font-size: 0.8rem;
        padding: 0.5rem 0.3rem !important;
    }
    /* 欢迎横幅 */
    .hero-section {
        padding: 1rem !important;
        margin-bottom: 1rem !important;
        margin-top: 0rem !important;
        min-height: 350px;
        text-align: center;
        justify-content: center;
    }

    .video-card .card-img-top {
        height: 150px;
    }

    .back-to-top {
        bottom: 15px;
        right: 15px;
        width: 45px;
        height: 45px;
    }

    .stats-section {
        padding: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .search-input-mobile {
        width: 100px;
    }



    .navbar {
        /* 小屏幕缩小后再增加2.5px = 41.5px */
        /* min-height: calc(44px - 5px + 2.5px) !important;  */
        padding-top: 0.2rem !important;
        padding-bottom: 0.2rem !important;
    }

    .navbar-nav .nav-link {
        font-size: 0.75rem;
        padding: 0.4rem 0.2rem !important;
    }

    .video-card .card-body {
        padding: 1rem;
    }

    .hero-section {
        padding: 0.8rem 1rem !important;
        margin-top: 0rem !important;
        min-height: 300px;
        text-align: center;
        justify-content: center;
    }
}

/* 动画效果 - 已禁用 */
/*
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}
*/

/* 加载动画 */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 首页页脚居中样式 */
.footer-single-container {
    text-align: center;
}

.footer-single-container .spqk01,
.footer-single-container .spqk02 {
    text-align: center;
    margin: 0 auto;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .video-card {
        background: #2d3748;
        color: #e2e8f0;
    }

    .video-card .card-title {
        color: #f7fafc;
    }

    .video-card .card-text {
        color: #a0aec0;
    }

    .stats-section {
        background: #2d3748;
        color: #e2e8f0;
    }

    .stat-label {
        color: #a0aec0;
    }

    .empty-state {
        color: #a0aec0;
    }

    .empty-state h3 {
        color: #e2e8f0;
    }
}

/* Footer单一容器 - 复制Bootstrap container样式 */
.footer-single-container {
    width: 100%;
    padding: 0px;
    margin: 0px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* 响应式断点 - 复制Bootstrap container断点 */
@media (min-width: 576px) {
    .footer-single-container {
        max-width: 540px;
    }
}

@media (min-width: 768px) {
    .footer-single-container {
        max-width: 720px;
    }
}

@media (min-width: 992px) {
    .footer-single-container {
        max-width: 960px;
    }
}

@media (min-width: 1200px) {
    .footer-single-container {
        max-width: 1140px;
    }
}

@media (min-width: 1400px) {
    .footer-single-container {
        max-width: 1320px;
    }
}

/* Footer margin调整 */
.bg-dark.text-light.py-4.mt-5 {
    font-size: 15px !important;
    margin-top: 1px !important;
    padding: 10px !important;
    display: flex;
    align-items: center;
}
.spqk01{
    font-size: 15px !important;
    margin: 0px !important;
    text-align: center;
    display: inline-block;
}
.spqk02{
    font-size: 15px !important;
    margin: 0px !important;
    text-align: center;
    display: inline-block;
    transform: translateX(-7px);
}

/* 视频网格布局 - 替代Bootstrap row/col */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin: 0;
    padding: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .video-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .video-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
}

/* 视频播放器容器样式 */
.video-player-container.mb-4 {
    border: 2px solid #ffffff !important;
}